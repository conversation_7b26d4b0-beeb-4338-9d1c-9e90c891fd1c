'use client';

import { useState, useEffect } from 'react';
import {
  MessageSquare,
  Mail,
  Phone,
  Calendar,
  Eye,
  CheckCircle,
  Clock,
  AlertCircle,
  Trash2,
  Check
} from 'lucide-react';

interface Contact {
  id: number;
  name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
  status: 'new' | 'read' | 'replied';
  is_read: boolean;
  created_at: string;
  site_id: number;
  site_name: string;
  site_code: string;
  source: string; // Kaynak bilgisi (mgsam, metaanalizgroup)
  source_table?: string;
}

export default function ContactsPage() {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDark, setIsDark] = useState(false);
  const [sourceFilter, setSourceFilter] = useState<string>('all'); // Kaynak filtresi
  const [activeTab, setActiveTab] = useState<'unread'>('unread');
  const [showHeaderCard, setShowHeaderCard] = useState(true);

  useEffect(() => {
    fetchContacts();

    // Check theme
    const savedTheme = localStorage.getItem('admin-theme');
    if (savedTheme === 'dark') {
      setIsDark(true);
    }

    // Check if header card was dismissed in last 24 hours
    const dismissedTime = localStorage.getItem('contacts-header-dismissed');
    if (dismissedTime) {
      const dismissedDate = new Date(dismissedTime);
      const now = new Date();
      const hoursDiff = (now.getTime() - dismissedDate.getTime()) / (1000 * 60 * 60);

      if (hoursDiff < 24) {
        setShowHeaderCard(false);
      }
    }

    // Listen for theme changes
    const handleThemeChange = (event: CustomEvent) => {
      setIsDark(event.detail.isDark);
    };

    window.addEventListener('themeChange', handleThemeChange as EventListener);

    return () => {
      window.removeEventListener('themeChange', handleThemeChange as EventListener);
    };
  }, []);

  const fetchContacts = async () => {
    try {
      const response = await fetch('/api/admin/contacts.php', {
        credentials: 'include'
      });
      if (response.ok) {
        const data = await response.json();
        setContacts(data.data?.contacts || []);
      }
    } catch (error) {
      console.error('Error fetching contacts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const markAsRead = async (contactId: number, sourceTable: string) => {
    try {
      const response = await fetch('/api/admin/contacts.php', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          id: contactId,
          action: 'mark_read',
          source_table: sourceTable
        }),
      });

      if (response.ok) {
        setContacts(prev =>
          prev.map(contact =>
            contact.id === contactId
              ? { ...contact, status: 'read' as const }
              : contact
          )
        );
      }
    } catch (error) {
      console.error('Error marking contact as read:', error);
    }
  };

  const getSiteBadge = (siteId: number, siteName: string) => {
    if (siteId === 1) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          Meta Analiz Group
        </span>
      );
    } else if (siteId === 2) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          Meta Analiz Müşavirlik
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          {siteName}
        </span>
      );
    }
  };

  const deleteContact = async (contactId: number) => {
    if (!confirm('Bu mesajı silmek istediğinizden emin misiniz?')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/contacts.php?id=${contactId}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      if (response.ok) {
        setContacts(prev => prev.filter(contact => contact.id !== contactId));
      }
    } catch (error) {
      console.error('Error deleting contact:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'new':
        return <AlertCircle className="h-4 w-4 text-amber-500" />;
      case 'read':
        return <Eye className="h-4 w-4 text-blue-500" />;
      case 'replied':
        return <CheckCircle className="h-4 w-4 text-emerald-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'new':
        return 'Yeni';
      case 'read':
        return 'Okundu';
      case 'replied':
        return 'Yanıtlandı';
      default:
        return 'Bekliyor';
    }
  };

  const getStatusColor = (status: string) => {
    const baseClasses = isDark ? 'border-gray-700' : 'border-gray-200';

    switch (status) {
      case 'new':
        return `${baseClasses} ${isDark ? 'bg-amber-900/20 border-amber-800' : 'bg-amber-50 border-amber-200'}`;
      case 'read':
        return `${baseClasses} ${isDark ? 'bg-blue-900/20 border-blue-800' : 'bg-blue-50 border-blue-200'}`;
      case 'replied':
        return `${baseClasses} ${isDark ? 'bg-emerald-900/20 border-emerald-800' : 'bg-emerald-50 border-emerald-200'}`;
      default:
        return `${baseClasses} ${isDark ? 'bg-gray-800' : 'bg-white'}`;
    }
  };

  // Kaynak badge rengi
  const getSourceBadge = (source: string) => {
    switch (source) {
      case 'mgsam':
        return 'bg-orange-100 text-orange-800 border border-orange-200';
      case 'metaanalizgroup':
        return 'bg-blue-100 text-blue-800 border border-blue-200';
      case 'metaanaliz-musavirlik':
        return 'bg-green-100 text-green-800 border border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border border-gray-200';
    }
  };

  // Kaynak adı
  const getSourceName = (source: string) => {
    switch (source) {
      case 'mgsam':
        return 'MGSAM';
      case 'metaanalizgroup':
        return 'Meta Analiz Group';
      case 'metaanaliz-musavirlik':
        return 'Meta Analiz Müşavirlik';
      default:
        return source?.toUpperCase() || 'Bilinmeyen';
    }
  };

  const filteredContacts = contacts.filter(contact => {
    // Kaynak filtresi
    if (sourceFilter !== 'all' && contact.source !== sourceFilter) {
      return false;
    }

    // Sadece okunmamış mesajları göster
    return contact.status === 'new';
  });

  const unreadCount = contacts.filter(contact => contact.status === 'new').length;



  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className={`animate-spin rounded-full h-12 w-12 border-b-2 ${
          isDark ? 'border-blue-400' : 'border-blue-600'
        }`}></div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDark ? 'bg-gray-900' : 'bg-gray-50'
    }`}>
      <div className="p-6">
        {/* Tabs and Filters */}
        <div className="flex flex-wrap gap-4 mb-6">
          {/* Status Tabs */}
          <div className="flex space-x-1">
            <button
              onClick={() => setActiveTab('unread')}
              className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                activeTab === 'unread'
                  ? 'bg-blue-600 text-white shadow-lg'
                  : isDark
                    ? 'text-gray-300 hover:bg-gray-700 hover:text-white'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              Okunmamış {unreadCount > 0 && `(${unreadCount})`}
            </button>
          </div>

          {/* Source Filter */}
          <div className="flex space-x-2">
            <button
              onClick={() => setSourceFilter('metaanalizgroup')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                sourceFilter === 'metaanalizgroup'
                  ? 'bg-blue-600 text-white shadow-lg'
                  : isDark
                    ? 'text-gray-300 hover:bg-gray-700 hover:text-white border border-gray-600'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900 border border-gray-300'
              }`}
            >
              Meta Analiz Group
            </button>
            <button
              onClick={() => setSourceFilter('metaanaliz-musavirlik')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                sourceFilter === 'metaanaliz-musavirlik'
                  ? 'bg-green-600 text-white shadow-lg'
                  : isDark
                    ? 'text-gray-300 hover:bg-gray-700 hover:text-white border border-gray-600'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900 border border-gray-300'
              }`}
            >
              Meta Analiz Müşavirlik
            </button>
            <button
              onClick={() => setSourceFilter('mgsam')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                sourceFilter === 'mgsam'
                  ? 'bg-orange-600 text-white shadow-lg'
                  : isDark
                    ? 'text-gray-300 hover:bg-gray-700 hover:text-white border border-gray-600'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900 border border-gray-300'
              }`}
            >
              MGSAM
            </button>
          </div>
        </div>

        {/* Contacts List */}
        <div className={`rounded-xl shadow-sm border ${
          isDark
            ? 'bg-gray-800 border-gray-700'
            : 'bg-white border-gray-200'
        }`}>
          <div className="p-6">
            {filteredContacts.length === 0 ? (
              <div className="text-center py-12">
                <MessageSquare className={`mx-auto h-12 w-12 mb-4 ${
                  isDark ? 'text-gray-600' : 'text-gray-400'
                }`} />
                <p className={`text-lg font-medium ${
                  isDark ? 'text-gray-300' : 'text-gray-600'
                }`}>
                  {activeTab === 'unread' ? 'Okunmamış mesaj bulunmuyor' : 'Henüz mesaj bulunmuyor'}
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {filteredContacts.map((contact) => (
                  <div
                    key={contact.id}
                    className={`p-3 rounded-lg border transition-all duration-200 hover:shadow-md max-w-4xl ${
                      isDark
                        ? 'bg-gray-700/50 border-gray-600 hover:border-gray-500'
                        : 'bg-gray-50 border-gray-200 hover:border-gray-300'
                    }`}
                  >
                  <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className={`font-medium text-sm ${
                          isDark ? 'text-white' : 'text-gray-900'
                        }`}>
                          {contact.name}
                        </h3>
                        <div className="flex items-center space-x-2">
                          {/* Source Badge - Sadece bir tane badge göster */}
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSourceBadge(contact.source || 'metaanalizgroup')}`}>
                            {getSourceName(contact.source || 'metaanalizgroup')}
                          </span>
                          <div className="flex items-center space-x-1">
                            {getStatusIcon(contact.status)}
                            <span className={`text-xs font-medium ${
                              isDark ? 'text-gray-400' : 'text-gray-600'
                            }`}>
                              {getStatusText(contact.status)}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-3 mb-2 text-xs">
                        <div className="flex items-center space-x-1 min-w-0">
                          <Mail className={`h-3 w-3 flex-shrink-0 ${
                            isDark ? 'text-gray-400' : 'text-gray-500'
                          }`} />
                          <span className={`truncate ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                            {contact.email}
                          </span>
                        </div>
                        {contact.phone && (
                          <div className="flex items-center space-x-1 min-w-0">
                            <Phone className={`h-3 w-3 flex-shrink-0 ${
                              isDark ? 'text-gray-400' : 'text-gray-500'
                            }`} />
                            <span className={`truncate ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                              {contact.phone}
                            </span>
                          </div>
                        )}
                        <div className="flex items-center space-x-1 min-w-0">
                          <Calendar className={`h-3 w-3 flex-shrink-0 ${
                            isDark ? 'text-gray-400' : 'text-gray-500'
                          }`} />
                          <span className={`truncate ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                            {new Date(contact.created_at).toLocaleDateString('tr-TR')}
                          </span>
                        </div>
                      </div>

                      <div className="mb-1">
                        <p className={`font-medium text-xs break-words ${
                          isDark ? 'text-gray-200' : 'text-gray-800'
                        }`}>
                          Konu: <span className="break-all">{contact.subject}</span>
                        </p>
                      </div>

                      <p className={`text-xs line-clamp-2 break-words ${
                        isDark ? 'text-gray-400' : 'text-gray-600'
                      }`}>
                        {contact.message}
                      </p>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center space-x-2 mt-3 sm:mt-0 sm:ml-4">
                      {contact.status === 'new' && (
                        <button
                          onClick={() => markAsRead(contact.id, contact.source_table || 'contact_messages')}
                          className={`p-2 rounded-lg transition-colors duration-200 ${
                            isDark
                              ? 'text-blue-400 hover:bg-blue-900/20 hover:text-blue-300'
                              : 'text-blue-600 hover:bg-blue-50 hover:text-blue-700'
                          }`}
                          title="Okundu olarak işaretle"
                        >
                          <Check className="h-4 w-4" />
                        </button>
                      )}

                      <button
                        onClick={() => deleteContact(contact.id)}
                        className={`p-2 rounded-lg transition-colors duration-200 ${
                          isDark
                            ? 'text-red-400 hover:bg-red-900/20 hover:text-red-300'
                            : 'text-red-600 hover:bg-red-50 hover:text-red-700'
                        }`}
                        title="Mesajı sil"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        </div>
      </div>
    </div>
  );
}
