'use client';

import { useEffect } from 'react';

declare global {
  namespace JSX {
    interface IntrinsicElements {
      'elevenlabs-convai': React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement> & {
          'agent-id': string;
        },
        HTMLElement
      >;
    }
  }
}

export default function ElevenLabsAgent() {
  useEffect(() => {
    // Check if script is already loaded to prevent duplicates
    if (document.querySelector('script[src="https://unpkg.com/@elevenlabs/convai-widget-embed"]')) {
      return;
    }

    // Check if custom element is already defined
    if (customElements.get('elevenlabs-convai')) {
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://unpkg.com/@elevenlabs/convai-widget-embed';
    script.async = true;
    script.type = 'text/javascript';
    
    script.onload = () => {
      console.log('ElevenLabs widget loaded successfully');
    };
    
    script.onerror = () => {
      console.error('Failed to load ElevenLabs widget');
    };
    
    document.head.appendChild(script);

    return () => {
      // Don't remove script on cleanup to prevent re-loading issues
      // The script can be reused across component mounts
    };
  }, []);

  return (
    <elevenlabs-convai 
      agent-id="agent_5101k13kaerbe8fbzmgerwps7q7b"
      className="w-full h-full"
    />
  );
}
