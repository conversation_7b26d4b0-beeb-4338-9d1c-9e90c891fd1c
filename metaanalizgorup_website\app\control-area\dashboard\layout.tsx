'use client';

import { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import {
  LayoutDashboard,
  MessageSquare,
  Calendar,
  Settings,
  LogOut,
  Menu,
  User,
  Sun,
  Moon,
  Bell,
  Phone,
  PenTool,
  ExternalLink,
  Newspaper
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { tr } from 'date-fns/locale/tr';

interface AdminUser {
  id: number;
  username: string;
  email: string;
  fullName: string;
  role: string;
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [user, setUser] = useState<AdminUser | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [isDark, setIsDark] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [notifications, setNotifications] = useState<any[]>([]);
  const [showNotifications, setShowNotifications] = useState(false);
  const [loadingNotifications, setLoadingNotifications] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  // Get page title based on current route
  const getPageTitle = () => {
    switch (pathname) {
      case '/control-area/dashboard':
        return 'Dashboard';
      case '/control-area/dashboard/articles':
        return 'Makaleler';
      case '/control-area/dashboard/news':
        return 'Haberler';
      case '/control-area/dashboard/contacts':
        return 'İletişim Mesajları';
      case '/control-area/dashboard/callbacks':
        return 'Geri Arama Talepleri';
      case '/control-area/dashboard/meetings':
        return 'Toplantı Talepleri';
      case '/control-area/dashboard/settings':
        return 'Site Ayarları';
      default:
        return 'Yönetim Paneli';
    }
  };

  useEffect(() => {
    setIsClient(true);
    
    // Get user info from localStorage
    const userData = localStorage.getItem('admin-user');
    if (userData) {
      setUser(JSON.parse(userData));
    }

    // Get theme preference
    const savedTheme = localStorage.getItem('admin-theme');
    if (savedTheme === 'dark') {
      setIsDark(true);
      document.documentElement.classList.add('dark');
    }

    // Get sidebar state
    const savedSidebarState = localStorage.getItem('admin-sidebar-open');
    if (savedSidebarState !== null) {
      setSidebarOpen(JSON.parse(savedSidebarState));
    }

    // Fetch stats
    fetchStats();

    // Stats için polling (her 30 saniyede bir)
    const interval = setInterval(fetchStats, 30000);

    return () => clearInterval(interval);
  }, []);

  const toggleTheme = () => {
    const newTheme = !isDark;
    setIsDark(newTheme);

    if (newTheme) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('admin-theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('admin-theme', 'light');
    }
  };

  const toggleSidebar = () => {
    const newState = !sidebarOpen;
    setSidebarOpen(newState);
    localStorage.setItem('admin-sidebar-open', JSON.stringify(newState));
  };

  const handleLogout = async () => {
    try {
      await fetch('/api/admin/logout.php', {
        method: 'POST',
      });
      localStorage.removeItem('admin-user');
      router.push('/control-area');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/dashboard-stats.php', {
        credentials: 'include',
      });
      const data = await response.json();

      if (data.success) {
        const totalUnread = (data.unread_contacts || 0) + (data.unread_callbacks || 0) + (data.unread_meetings || 0);
        setUnreadCount(totalUnread);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const fetchNotifications = async () => {
    setLoadingNotifications(true);
    try {
      const response = await fetch('/api/admin/notifications.php', {
        credentials: 'include',
      });
      const data = await response.json();
      if (data.success) {
        setNotifications(data.data.notifications || []);
      }
    } catch (error) {
      setNotifications([]);
    } finally {
      setLoadingNotifications(false);
    }
  };

  const handleNotificationsClick = () => {
    setShowNotifications((prev) => !prev);
    if (!showNotifications) {
      fetchNotifications();
    }
  };

  const navigation = [
    {
      name: 'Dashboard',
      href: '/control-area/dashboard',
      icon: LayoutDashboard,
    },
    {
      name: 'Makaleler',
      href: '/control-area/dashboard/articles',
      icon: PenTool,
    },
    {
      name: 'Haberler',
      href: '/control-area/dashboard/news',
      icon: Newspaper,
    },
    {
      name: 'İletişim Mesajları',
      href: '/control-area/dashboard/contacts',
      icon: MessageSquare,
    },
    {
      name: 'Geri Arama Talepleri',
      href: '/control-area/dashboard/callbacks',
      icon: Phone,
    },
    {
      name: 'Toplantı Talepleri',
      href: '/control-area/dashboard/meetings',
      icon: Calendar,
    },
    {
      name: 'Site Ayarları',
      href: '/control-area/dashboard/settings',
      icon: Settings,
    },
    {
      name: 'Sevgi Plajı Koop',
      href: 'https://www.sevgiplajikoop.com/admin',
      icon: ExternalLink,
      external: true,
    },
  ];

  // Client-side olmadığında loading göster
  if (!isClient) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Yükleniyor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDark ? 'bg-gray-900' : 'bg-gray-50'
    }`}>
      <div className="flex h-screen">
        {/* Sidebar */}
        <div className={`fixed inset-y-0 left-0 z-30 transform transition-all duration-300 ease-in-out flex flex-col ${
          sidebarOpen ? 'w-64 translate-x-0' : 'w-16 translate-x-0'
        } ${
          isDark ? 'bg-gray-800' : 'bg-white'
        }`}>
          {/* Header with hamburger and logo */}
          <div className={`flex items-center h-16 ${
            sidebarOpen ? 'px-4' : 'px-2'
          }`}>
            {/* Hamburger icon - always visible on left, aligned with nav icons */}
            <button
              onClick={toggleSidebar}
              className={`flex-shrink-0 p-2 rounded-lg transition-colors duration-200 ${
                isDark
                  ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
              }`}
            >
              <Menu className="h-5 w-5" />
            </button>

            {/* Logo - only when sidebar is open */}
            {sidebarOpen && (
              <div className="flex-1 flex justify-center ml-2">
                <Image
                  src="/meta_group_logo.webp"
                  alt="Meta Analiz Group"
                  width={120}
                  height={38}
                  className={`object-contain ${
                    isDark ? 'brightness-0 invert' : 'brightness-0'
                  }`}
                  priority
                />
              </div>
            )}
          </div>

          <nav className="flex-1 flex flex-col h-full">
            {/* Navigation Menu */}
            <div className={`flex-1 ${
              sidebarOpen ? 'px-4 space-y-2 mt-6' : 'px-2 flex flex-col justify-start space-y-2 mt-2'
            }`}>
              {navigation.map((item) => (
                item.external ? (
                  <a
                    key={item.name}
                    href={item.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`flex items-center text-sm font-medium rounded-xl transition-all duration-200 group ${
                      sidebarOpen ? 'px-4 py-3' : 'px-2 py-3 justify-center'
                    } ${
                      isDark
                        ? 'text-gray-300 hover:bg-gray-700 hover:text-white'
                        : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700'
                    }`}
                    title={!sidebarOpen ? item.name : undefined}
                  >
                    <item.icon className={`h-5 w-5 transition-colors duration-200 ${
                      sidebarOpen ? 'mr-3' : ''
                    } ${
                      isDark
                        ? 'text-gray-400 group-hover:text-blue-400'
                        : 'text-gray-500 group-hover:text-blue-600'
                    }`} />
                    {sidebarOpen && item.name}
                  </a>
                ) : (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`flex items-center text-sm font-medium rounded-xl transition-all duration-200 group ${
                      sidebarOpen ? 'px-4 py-3' : 'px-2 py-3 justify-center'
                    } ${
                      isDark
                        ? 'text-gray-300 hover:bg-gray-700 hover:text-white'
                        : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700'
                    }`}
                    title={!sidebarOpen ? item.name : undefined}
                  >
                    <item.icon className={`h-5 w-5 transition-colors duration-200 ${
                      sidebarOpen ? 'mr-3' : ''
                    } ${
                      isDark
                        ? 'text-gray-400 group-hover:text-blue-400'
                        : 'text-gray-500 group-hover:text-blue-600'
                    }`} />
                    {sidebarOpen && item.name}
                  </Link>
                )
              ))}
            </div>

            {/* User Profile Section */}
            <div className={`mb-4 ${sidebarOpen ? 'px-4' : 'px-2'}`}>
              <div className={`flex items-center rounded-xl transition-colors duration-200 ${
                sidebarOpen ? 'space-x-3 p-4' : 'justify-center p-2'
              } ${
                isDark ? 'bg-gray-700/50' : 'bg-gradient-to-r from-blue-50 to-indigo-50'
              }`}>
                <div className={`flex-shrink-0 p-2 rounded-lg ${
                  isDark ? 'bg-gray-600' : 'bg-white'
                }`}>
                  <User className={`h-6 w-6 ${
                    isDark ? 'text-gray-300' : 'text-blue-600'
                  }`} />
                </div>
                {sidebarOpen && (
                  <div className="flex-1 min-w-0">
                    <p className={`text-sm font-semibold truncate ${
                      isDark ? 'text-gray-200' : 'text-gray-900'
                    }`}>
                      {user?.fullName ?? 'Admin User'}
                    </p>
                    <p className={`text-xs truncate ${
                      isDark ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      {user?.email ?? '<EMAIL>'}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Logout Button */}
            <div className={sidebarOpen ? 'p-4' : 'p-2'}>
              <button
                onClick={handleLogout}
                className={`flex items-center w-full text-sm font-medium rounded-xl transition-all duration-200 ${
                  sidebarOpen ? 'px-4 py-3' : 'px-2 py-3 justify-center'
                } ${
                  isDark
                    ? 'text-red-400 hover:bg-red-900/20 hover:text-red-300'
                    : 'text-red-600 hover:bg-red-50 hover:text-red-700'
                }`}
                title={!sidebarOpen ? 'Çıkış Yap' : undefined}
              >
                <LogOut className={`h-5 w-5 ${sidebarOpen ? 'mr-3' : ''}`} />
                {sidebarOpen && 'Çıkış Yap'}
              </button>
            </div>
          </nav>
        </div>

        {/* Main content */}
        <div className={`flex-1 flex flex-col overflow-hidden transition-all duration-300 ${
          sidebarOpen ? 'ml-64' : 'ml-16'
        }`}>
          {/* Top bar */}
          <div className={`sticky top-0 z-10 transition-colors duration-300 ${
            isDark ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className="flex items-center justify-between h-16 px-6">
              <div className="flex items-center space-x-4">
                <h1 className={`text-lg font-semibold ${
                  isDark ? 'text-gray-200' : 'text-gray-800'
                }`}>
                  {getPageTitle()}
                </h1>
              </div>

              {/* Right side - Theme Toggle and Notifications */}
              <div className="flex items-center space-x-4">
                {/* Theme Toggle */}
                <button
                  onClick={toggleTheme}
                  className={`p-2 rounded-lg transition-colors duration-200 ${
                    isDark
                      ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                  }`}
                  title={isDark ? 'Açık temaya geç' : 'Koyu temaya geç'}
                >
                  {isDark ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
                </button>

                {/* Notifications */}
                <div className="relative">
                  <button
                    className={`relative p-2 rounded-lg transition-colors duration-200 ${
                      isDark
                        ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700'
                        : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                    }`}
                    onClick={handleNotificationsClick}
                  >
                    <Bell className="h-6 w-6" />
                    {unreadCount > 0 && (
                      <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                        {unreadCount > 9 ? '9+' : unreadCount}
                      </span>
                    )}
                  </button>
                  {showNotifications && (
                    <div className={`absolute right-0 mt-2 w-96 max-w-full bg-white dark:bg-gray-800 shadow-lg rounded-xl z-50 border border-gray-200 dark:border-gray-700`}>
                      <div className="p-4 border-b border-gray-100 dark:border-gray-700 font-semibold text-gray-700 dark:text-gray-200">Bildirimler</div>
                      <div className="max-h-96 overflow-y-auto divide-y divide-gray-100 dark:divide-gray-700">
                        {loadingNotifications ? (
                          <div className="p-4 text-center text-gray-500 dark:text-gray-400">Yükleniyor...</div>
                        ) : notifications.length === 0 ? (
                          <div className="p-4 text-center text-gray-500 dark:text-gray-400">Yeni bildirim yok</div>
                        ) : (
                          notifications.map((notif, idx) => (
                            <a
                              key={notif.id || idx}
                              href={notif.link}
                              className="block px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                              target="_self"
                            >
                              <div className="flex items-center justify-between">
                                <div>
                                  <div className="font-medium text-gray-900 dark:text-white">{notif.title}</div>
                                  <div className="text-xs text-gray-500 dark:text-gray-300">{notif.message}</div>
                                </div>
                                <div className="text-xs text-gray-400 ml-2 whitespace-nowrap">
                                  {notif.created_at ? formatDistanceToNow(new Date(notif.created_at), { addSuffix: true, locale: tr }) : ''}
                                </div>
                              </div>
                            </a>
                          ))
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Page content */}
          <main className={`flex-1 overflow-y-auto p-6 transition-colors duration-300 ${
            isDark ? 'bg-gray-900' : 'bg-gray-50'
          }`}>
            {children}
          </main>
        </div>
      </div>
    </div>
  );
}
