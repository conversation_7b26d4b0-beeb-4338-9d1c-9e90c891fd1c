<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Haber veritabanı bağlantısı
$news_host = 'localhost';
$news_dbname = 'metaanalizhaber_mah';
$news_username = 'metaanalizhaber_oz';
$news_password = 'Mah2025!';

try {
    $news_pdo = new PDO("mysql:host=$news_host;dbname=$news_dbname;charset=utf8mb4", $news_username, $news_password);
    $news_pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Haber veritabanı bağlantı hatası: ' . $e->getMessage()]);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

switch ($method) {
    case 'GET':
        handleGet();
        break;
    case 'POST':
        handlePost();
        break;
    case 'PUT':
        handlePut();
        break;
    case 'DELETE':
        handleDelete();
        break;
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        break;
}

function handleGet() {
    global $news_pdo;
    
    // Handle categories endpoint
    if (isset($_GET['action']) && $_GET['action'] === 'categories') {
        try {
            $stmt = $news_pdo->prepare("SELECT DISTINCT kategori FROM news WHERE kategori IS NOT NULL AND kategori != '' ORDER BY kategori");
            $stmt->execute();
            $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            echo json_encode([
                'success' => true, 
                'data' => $categories
            ]);
            return;
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Kategori verisi alınamadı: ' . $e->getMessage()]);
            return;
        }
    }
    
    // Handle cities endpoint
    if (isset($_GET['action']) && $_GET['action'] === 'cities') {
        try {
            $stmt = $news_pdo->prepare("SELECT DISTINCT sehir FROM news WHERE sehir IS NOT NULL AND sehir != '' ORDER BY sehir");
            $stmt->execute();
            $cities = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            echo json_encode([
                'success' => true, 
                'data' => $cities
            ]);
            return;
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Şehir verisi alınamadı: ' . $e->getMessage()]);
            return;
        }
    }
    
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = 20;
    $offset = ($page - 1) * $limit;
    $search = isset($_GET['search']) ? $_GET['search'] : '';
    
    try {
        // Total count for pagination
        $countSql = "SELECT COUNT(*) FROM news WHERE status != 'deleted'";
        $countParams = [];
        
        if ($search) {
            $countSql .= " AND (title LIKE ? OR description LIKE ? OR haber_kodu LIKE ?)";
            $searchTerm = "%$search%";
            $countParams = [$searchTerm, $searchTerm, $searchTerm];
        }
        
        $countStmt = $news_pdo->prepare($countSql);
        $countStmt->execute($countParams);
        $totalCount = $countStmt->fetchColumn();
        
        // Get news with pagination
        $sql = "SELECT 
                    id, 
                    haber_kodu, 
                    ust_kategori, 
                    kategori, 
                    sehir, 
                    son_dakika, 
                    title, 
                    slug, 
                    description, 
                    pub_date, 
                    view_count, 
                    status,
                    has_images,
                    has_videos,
                    created_at,
                    updated_at
                FROM news 
                WHERE status != 'deleted'";
        
        $params = [];
        
        if ($search) {
            $sql .= " AND (title LIKE ? OR description LIKE ? OR haber_kodu LIKE ?)";
            $searchTerm = "%$search%";
            $params = [$searchTerm, $searchTerm, $searchTerm];
        }
        
        $sql .= " ORDER BY pub_date DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $news_pdo->prepare($sql);
        $stmt->execute($params);
        $news = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'data' => [
                'news' => $news,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => ceil($totalCount / $limit),
                    'total_count' => $totalCount,
                    'per_page' => $limit
                ]
            ]
        ]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Haberler alınırken hata: ' . $e->getMessage()]);
    }
}

function handlePost() {
    global $news_pdo, $input;
    
    $required_fields = ['title', 'description', 'kategori'];
    foreach ($required_fields as $field) {
        if (empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => "Gerekli alan eksik: $field"]);
            return;
        }
    }
    
    try {
        // Generate unique haber_kodu
        $haber_kodu = 'HBR_' . date('Ymd') . '_' . substr(md5(uniqid()), 0, 8);
        
        // Generate slug from title
        $slug = createSlug($input['title']);
        
        // Set publication date to current time if not provided
        $pub_date = date('Y-m-d H:i:s');
        
        $sql = "INSERT INTO news (
                    haber_kodu, 
                    kategori, 
                    sehir, 
                    son_dakika, 
                    title, 
                    slug, 
                    description, 
                    pub_date, 
                    status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $news_pdo->prepare($sql);
        $result = $stmt->execute([
            $haber_kodu,
            $input['kategori'],
            $input['sehir'] ?? '',
            $input['son_dakika'] ?? 'Hayır',
            $input['title'],
            $slug,
            $input['description'],
            $pub_date,
            $input['status'] ?? 'active'
        ]);
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Haber başarıyla eklendi']);
        } else {
            throw new Exception('Haber eklenirken hata oluştu');
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Haber eklenirken hata: ' . $e->getMessage()]);
    }
}

function handlePut() {
    global $news_pdo, $input;
    
    if (empty($input['id'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Haber ID gerekli']);
        return;
    }
    
    try {
        $updateFields = [];
        $params = [];
        
        if (!empty($input['title'])) {
            $updateFields[] = "title = ?";
            $params[] = $input['title'];
            
            // Update slug if title changed
            $updateFields[] = "slug = ?";
            $params[] = createSlug($input['title']);
        }
        
        if (!empty($input['description'])) {
            $updateFields[] = "description = ?";
            $params[] = $input['description'];
        }
        
        if (!empty($input['ust_kategori'])) {
            $updateFields[] = "ust_kategori = ?";
            $params[] = $input['ust_kategori'];
        }
        
        if (!empty($input['kategori'])) {
            $updateFields[] = "kategori = ?";
            $params[] = $input['kategori'];
        }
        
        if (isset($input['sehir'])) {
            $updateFields[] = "sehir = ?";
            $params[] = $input['sehir'];
        }
        
        if (isset($input['son_dakika'])) {
            $updateFields[] = "son_dakika = ?";
            $params[] = $input['son_dakika'];
        }
        
        if (isset($input['status'])) {
            $updateFields[] = "status = ?";
            $params[] = $input['status'];
        }
        
        if (empty($updateFields)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Güncellenecek alan belirtilmedi']);
            return;
        }
        
        $updateFields[] = "son_haber_guncelleme_tarihi = ?";
        $params[] = date('Y-m-d H:i:s');
        
        $params[] = $input['id'];
        
        $sql = "UPDATE news SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $news_pdo->prepare($sql);
        $result = $stmt->execute($params);
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Haber başarıyla güncellendi']);
        } else {
            throw new Exception('Haber güncellenirken hata oluştu');
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Haber güncellenirken hata: ' . $e->getMessage()]);
    }
}

function handleDelete() {
    global $news_pdo;
    
    $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    
    if (!$id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Haber ID gerekli']);
        return;
    }
    
    try {
        // Soft delete
        $stmt = $news_pdo->prepare("UPDATE news SET status = 'deleted' WHERE id = ?");
        $result = $stmt->execute([$id]);
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Haber başarıyla silindi']);
        } else {
            throw new Exception('Haber silinirken hata oluştu');
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Haber silinirken hata: ' . $e->getMessage()]);
    }
}

function createSlug($title) {
    // Turkish character replacements
    $search = ['ç', 'ğ', 'ı', 'ö', 'ş', 'ü', 'Ç', 'Ğ', 'I', 'İ', 'Ö', 'Ş', 'Ü'];
    $replace = ['c', 'g', 'i', 'o', 's', 'u', 'c', 'g', 'i', 'i', 'o', 's', 'u'];
    
    $slug = str_replace($search, $replace, $title);
    $slug = strtolower($slug);
    $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
    $slug = trim(preg_replace('/[\s-]+/', '-', $slug), '-');
    
    return $slug;
}
?>
