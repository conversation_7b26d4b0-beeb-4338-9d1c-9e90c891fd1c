import Link from 'next/link';
import Image from 'next/image';

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-6 py-12">

        {/* Mobile Layout */}
        <div className="block md:hidden">
          {/* Logo - Centered */}
          <div className="text-center mb-8">
            <div className="relative w-[180px] h-[40px] mx-auto">
              <Image
                src="/logo6.webp"
                alt="Meta Analiz Müşavirlik Logo"
                fill
                className="object-contain brightness-0 invert"
                sizes="180px"
                priority
              />
            </div>
          </div>

          {/* Company Info - Centered */}
          <div className="text-center mb-8">
            <p className="text-gray-300 mb-4 leading-relaxed text-sm">
              2011 yılından bu yana müşterilerimize profesyonel danışmanlık hizmetleri sunmaktayız.
            </p>
            <div className="space-y-2 text-sm">
              <p className="text-gray-300">
                <span className="font-semibold">Telefon:</span> 0 (542) 797 05 00
              </p>
              <p className="text-gray-300">
                <span className="font-semibold">E-posta:</span> <EMAIL> - <EMAIL>
              </p>
            </div>
          </div>
        </div>

        {/* Desktop Layout */}
        <div className="hidden md:grid md:grid-cols-2 lg:grid-cols-4 gap-8">

          {/* Company Info */}
          <div className="lg:col-span-2">
            <div className="flex items-center mb-6">
              <div className="relative w-[180px] h-[40px]">
                <Image
                  src="/logo6.webp"
                  alt="Meta Analiz Müşavirlik Logo"
                  fill
                  className="object-contain brightness-0 invert"
                  sizes="180px"
                  priority
                />
              </div>
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed">
              2011 yılından bu yana müşterilerimize Yönetim ve Yatırım Müşavirliği,
              Finansal ve Mali Danışmanlık, İş ve Sosyal Güvenlik Müşavirliği,
              İnsan Kaynakları hizmetleri sunmaktayız.
            </p>
            <div className="space-y-2">
              <p className="text-gray-300">
                <span className="font-semibold">Adres:</span> Yenicami Mah. Özmen Sok. No: 24/A Söke Aydın
              </p>
              <p className="text-gray-300">
                <span className="font-semibold">Telefon:</span> 0 (542) 380 00 50
              </p>
              <p className="text-gray-300">
                <span className="font-semibold">E-posta:</span> <EMAIL> - <EMAIL>
              </p>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Hızlı Linkler</h4>
            <ul className="space-y-3">
              <li>
                <Link href="/hakkimizda" className="text-gray-300 hover:text-white transition-colors">
                  Hakkımızda
                </Link>
              </li>
              <li>
                <Link href="/hizmetlerimiz" className="text-gray-300 hover:text-white transition-colors">
                  Hizmetlerimiz
                </Link>
              </li>
              <li>
                <Link href="/makalelerimiz" className="text-gray-300 hover:text-white transition-colors">
                  Makalelerimiz
                </Link>
              </li>
              <li>
                <Link href="/iletisim" className="text-gray-300 hover:text-white transition-colors">
                  İletişim
                </Link>
              </li>
              <li>
                <Link href="/toplanti-planla" className="text-gray-300 hover:text-white transition-colors">
                  Toplantı Planla
                </Link>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Hizmetlerimiz</h4>
            <ul className="space-y-3">
              <li>
                <Link href="/hizmetlerimiz/yonetim-ve-yatirim-musavirligi" className="text-gray-300 hover:text-white transition-colors">
                  Yönetim ve Yatırım Müşavirliği
                </Link>
              </li>
              <li>
                <Link href="/hizmetlerimiz/mali-musavirlik" className="text-gray-300 hover:text-white transition-colors">
                  Finansal ve Mali Danışmanlık/Müşavirlik
                </Link>
              </li>
              <li>
                <Link href="/hizmetlerimiz/sgk-uyusmazliklari" className="text-gray-300 hover:text-white transition-colors">
                  İş ve Sosyal Güvenlik Müşavirliği
                </Link>
              </li>
              <li>
                <Link href="/hizmetlerimiz/insan-kaynaklari" className="text-gray-300 hover:text-white transition-colors">
                  İnsan Kaynakları
                </Link>
              </li>
              <li>
                <Link href="/hizmetlerimiz/kurumsal-danismanlik" className="text-gray-300 hover:text-white transition-colors">
                  Kurumsal Danışmanlık
                </Link>
              </li>
            </ul>
          </div>

        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-xs md:text-sm text-center md:text-left">
              © 2025 Meta Analiz Müşavirlik. Tüm hakları saklıdır.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link href="/gizlilik-politikasi" className="text-gray-400 hover:text-white text-sm transition-colors">
                Gizlilik Politikası
              </Link>
              <Link href="/kullanim-kosullari" className="text-gray-400 hover:text-white text-sm transition-colors">
                Kullanım Koşulları
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
