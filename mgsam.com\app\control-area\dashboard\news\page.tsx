'use client';

import { useState, useEffect } from 'react';
import {
  Newspaper,
  Plus,
  Edit,
  Trash2,
  Eye,
  Calendar,
  Search,
  Filter,
  CheckCircle,
  Clock,
  Archive,
  Save,
  AlertCircle,
  Globe,
  MapPin,
  Hash
} from 'lucide-react';

interface News {
  id: number;
  haber_kodu: string;
  ust_kategori: string;
  kategori: string;
  sehir: string;
  son_dakika: 'Evet' | 'Hayır';
  title: string;
  slug: string;
  description: string;
  pub_date: string;
  view_count: number;
  status: 'active' | 'inactive' | 'deleted';
  has_images: boolean;
  has_videos: boolean;
  created_at: string;
  updated_at: string;
}

interface Pagination {
  current_page: number;
  total_pages: number;
  total_count: number;
  per_page: number;
}

export default function NewsPage() {
  const [news, setNews] = useState<News[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [cities, setCities] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDark, setIsDark] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState<'list' | 'add'>('add');
  const [editingNews, setEditingNews] = useState<News | null>(null);
  const [pagination, setPagination] = useState<Pagination>({
    current_page: 1,
    total_pages: 1,
    total_count: 0,
    per_page: 20
  });
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    kategori: '',
    sehir: '',
    son_dakika: 'Hayır' as 'Evet' | 'Hayır',
    status: 'active' as 'active' | 'inactive'
  });
  const [isSaving, setIsSaving] = useState(false);

  // Theme detection
  useEffect(() => {
    const checkTheme = () => {
      const isDarkMode = document.documentElement.classList.contains('dark') || 
                        document.body.classList.contains('dark');
      setIsDark(isDarkMode);
    };

    checkTheme();
    
    const observer = new MutationObserver(checkTheme);
    observer.observe(document.documentElement, { 
      attributes: true, 
      attributeFilter: ['class'] 
    });
    observer.observe(document.body, { 
      attributes: true, 
      attributeFilter: ['class'] 
    });

    window.addEventListener('themeChange', checkTheme);

    return () => {
      observer.disconnect();
      window.removeEventListener('themeChange', checkTheme);
    };
  }, []);

  // Load news
  useEffect(() => {
    loadNews();
  }, [pagination.current_page, searchTerm]);

  // Load categories and cities
  useEffect(() => {
    loadCategories();
    loadCities();
  }, []);

  const loadCategories = async () => {
    try {
      const response = await fetch('/api/admin/news.php?action=categories');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setCategories(data.data || []);
        }
      }
    } catch (error) {
      console.error('Kategoriler yüklenirken hata:', error);
    }
  };

  const loadCities = async () => {
    try {
      const response = await fetch('/api/admin/news.php?action=cities');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setCities(data.data || []);
        }
      }
    } catch (error) {
      console.error('Şehirler yüklenirken hata:', error);
    }
  };

  const loadNews = async () => {
    try {
      setIsLoading(true);
      const searchParam = searchTerm ? `&search=${encodeURIComponent(searchTerm)}` : '';
      const response = await fetch(`/api/admin/news.php?page=${pagination.current_page}${searchParam}`);
      
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setNews(data.data.news || []);
          setPagination(data.data.pagination);
        }
      }
    } catch (error) {
      console.error('Haberler yüklenirken hata:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim() || !formData.description.trim()) {
      alert('Başlık ve içerik alanları zorunludur!');
      return;
    }

    setIsSaving(true);
    
    try {
      const url = editingNews ? '/api/admin/news.php' : '/api/admin/news.php';
      const method = editingNews ? 'PUT' : 'POST';
      const payload = editingNews 
        ? { ...formData, id: editingNews.id }
        : { ...formData, pub_date: new Date().toISOString().slice(0, 19).replace('T', ' ') };

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();
      
      if (data.success) {
        alert(editingNews ? 'Haber başarıyla güncellendi!' : 'Haber başarıyla eklendi!');
        resetForm();
        setActiveTab('list');
        loadNews();
      } else {
        alert('Hata: ' + (data.message || 'Bilinmeyen hata'));
      }
    } catch (error) {
      console.error('Haber kaydedilirken hata:', error);
      alert('Haber kaydedilirken hata oluştu!');
    } finally {
      setIsSaving(false);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      kategori: '',
      sehir: '',
      son_dakika: 'Hayır',
      status: 'active'
    });
    setEditingNews(null);
  };

  const handleEdit = (newsItem: News) => {
    setFormData({
      title: newsItem.title,
      description: newsItem.description,
      kategori: newsItem.kategori,
      sehir: newsItem.sehir,
      son_dakika: newsItem.son_dakika,
      status: newsItem.status === 'deleted' ? 'inactive' : newsItem.status
    });
    setEditingNews(newsItem);
    setActiveTab('add');
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Bu haberi silmek istediğinizden emin misiniz?')) return;

    try {
      const response = await fetch(`/api/admin/news.php?id=${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      
      if (data.success) {
        alert('Haber başarıyla silindi!');
        loadNews();
      } else {
        alert('Hata: ' + (data.message || 'Bilinmeyen hata'));
      }
    } catch (error) {
      console.error('Haber silinirken hata:', error);
      alert('Haber silinirken hata oluştu!');
    }
  };

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPagination(prev => ({ ...prev, current_page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, current_page: page }));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return isDark ? 'text-green-400' : 'text-green-600';
      case 'inactive':
        return isDark ? 'text-yellow-400' : 'text-yellow-600';
      default:
        return isDark ? 'text-gray-400' : 'text-gray-600';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Aktif';
      case 'inactive':
        return 'Pasif';
      default:
        return 'Bilinmeyen';
    }
  };

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className={`border-b ${isDark ? 'border-gray-700' : 'border-gray-200'}`}>
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => {
              setActiveTab('add');
              if (!editingNews) resetForm();
            }}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
              activeTab === 'add'
                ? isDark
                  ? 'border-blue-400 text-blue-400'
                  : 'border-blue-500 text-blue-600'
                : isDark
                  ? 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Plus className="w-4 h-4 inline mr-2" />
            Yeni Haber Ekle
          </button>
          <button
            onClick={() => setActiveTab('list')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
              activeTab === 'list'
                ? isDark
                  ? 'border-blue-400 text-blue-400'
                  : 'border-blue-500 text-blue-600'
                : isDark
                  ? 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Newspaper className="w-4 h-4 inline mr-2" />
            Tüm Haberler ({pagination.total_count})
          </button>
        </nav>
      </div>

      {/* News List Tab */}
      {activeTab === 'list' && (
        <>
          {/* Search */}
          <div className={`rounded-xl p-6 ${isDark ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 ${
                    isDark ? 'text-gray-400' : 'text-gray-500'
                  }`} />
                  <input
                    type="text"
                    placeholder="Haber ara (başlık, içerik, haber kodu)..."
                    value={searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                    className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      isDark
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    }`}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* News Table */}
          <div className={`rounded-xl ${isDark ? 'bg-gray-800' : 'bg-white'} shadow-sm overflow-hidden`}>
            {isLoading ? (
              <div className="p-8 text-center">
                <div className={`text-lg ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                  Haberler yükleniyor...
                </div>
              </div>
            ) : news.length === 0 ? (
              <div className="p-8 text-center">
                <Newspaper className={`w-12 h-12 mx-auto mb-4 ${isDark ? 'text-gray-400' : 'text-gray-500'}`} />
                <h3 className={`text-lg font-medium mb-2 ${isDark ? 'text-gray-300' : 'text-gray-900'}`}>
                  Henüz haber yok
                </h3>
                <p className={`${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                  İlk haberinizi eklemek için "Yeni Haber Ekle" sekmesini kullanın.
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'}`}>
                    <tr>
                      <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                        isDark ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        Haber Bilgileri
                      </th>
                      <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                        isDark ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        Kategori
                      </th>
                      <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                        isDark ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        Durum
                      </th>
                      <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                        isDark ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        İstatistik
                      </th>
                      <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                        isDark ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        İşlemler
                      </th>
                    </tr>
                  </thead>
                  <tbody className={`divide-y ${isDark ? 'divide-gray-700' : 'divide-gray-200'}`}>
                    {news.map((newsItem) => (
                      <tr key={newsItem.id} className={isDark ? 'hover:bg-gray-700' : 'hover:bg-gray-50'}>
                        <td className="px-6 py-4">
                          <div>
                            <div className={`text-sm font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
                              {newsItem.title}
                            </div>
                            <div className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'} flex items-center gap-2 mt-1`}>
                              <Hash className="w-3 h-3" />
                              {newsItem.haber_kodu}
                              {newsItem.sehir && (
                                <>
                                  <MapPin className="w-3 h-3 ml-2" />
                                  {newsItem.sehir}
                                </>
                              )}
                            </div>
                            <div className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'} mt-1`}>
                              <Calendar className="w-3 h-3 inline mr-1" />
                              {formatDate(newsItem.pub_date)}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-900'}`}>
                            {newsItem.ust_kategori}
                          </div>
                          <div className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                            {newsItem.kategori}
                          </div>
                          {newsItem.son_dakika === 'Evet' && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-1">
                              <AlertCircle className="w-3 h-3 mr-1" />
                              Son Dakika
                            </span>
                          )}
                        </td>
                        <td className="px-6 py-4">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            newsItem.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {newsItem.status === 'active' ? (
                              <CheckCircle className="w-3 h-3 mr-1" />
                            ) : (
                              <Clock className="w-3 h-3 mr-1" />
                            )}
                            {getStatusText(newsItem.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <div className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-900'}`}>
                            <Eye className="w-4 h-4 inline mr-1" />
                            {newsItem.view_count} görüntülenme
                          </div>
                          <div className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'} flex items-center gap-2 mt-1`}>
                            {newsItem.has_images && (
                              <span className="flex items-center">
                                📷 Resim
                              </span>
                            )}
                            {newsItem.has_videos && (
                              <span className="flex items-center">
                                🎥 Video
                              </span>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleEdit(newsItem)}
                              className={`p-2 rounded-lg transition-colors duration-200 ${
                                isDark
                                  ? 'text-blue-400 hover:text-blue-300 hover:bg-gray-700'
                                  : 'text-blue-600 hover:text-blue-700 hover:bg-gray-100'
                              }`}
                              title="Düzenle"
                            >
                              <Edit className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleDelete(newsItem.id)}
                              className={`p-2 rounded-lg transition-colors duration-200 ${
                                isDark
                                  ? 'text-red-400 hover:text-red-300 hover:bg-gray-700'
                                  : 'text-red-600 hover:text-red-700 hover:bg-gray-100'
                              }`}
                              title="Sil"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {/* Pagination */}
            {pagination.total_pages > 1 && (
              <div className={`px-6 py-4 border-t ${isDark ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'}`}>
                <div className="flex items-center justify-between">
                  <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-700'}`}>
                    {pagination.total_count} haberden {((pagination.current_page - 1) * pagination.per_page) + 1}-
                    {Math.min(pagination.current_page * pagination.per_page, pagination.total_count)} arası gösteriliyor
                  </div>
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={() => handlePageChange(pagination.current_page - 1)}
                      disabled={pagination.current_page <= 1}
                      className={`px-3 py-1 rounded-lg text-sm transition-colors duration-200 ${
                        pagination.current_page <= 1
                          ? isDark
                            ? 'text-gray-600 cursor-not-allowed'
                            : 'text-gray-400 cursor-not-allowed'
                          : isDark
                            ? 'text-gray-300 hover:bg-gray-700'
                            : 'text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      Önceki
                    </button>
                    
                    {Array.from({ length: Math.min(5, pagination.total_pages) }, (_, i) => {
                      const page = i + 1;
                      return (
                        <button
                          key={page}
                          onClick={() => handlePageChange(page)}
                          className={`px-3 py-1 rounded-lg text-sm transition-colors duration-200 ${
                            page === pagination.current_page
                              ? isDark
                                ? 'bg-blue-600 text-white'
                                : 'bg-blue-500 text-white'
                              : isDark
                                ? 'text-gray-300 hover:bg-gray-700'
                                : 'text-gray-700 hover:bg-gray-200'
                          }`}
                        >
                          {page}
                        </button>
                      );
                    })}
                    
                    <button
                      onClick={() => handlePageChange(pagination.current_page + 1)}
                      disabled={pagination.current_page >= pagination.total_pages}
                      className={`px-3 py-1 rounded-lg text-sm transition-colors duration-200 ${
                        pagination.current_page >= pagination.total_pages
                          ? isDark
                            ? 'text-gray-600 cursor-not-allowed'
                            : 'text-gray-400 cursor-not-allowed'
                          : isDark
                            ? 'text-gray-300 hover:bg-gray-700'
                            : 'text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      Sonraki
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </>
      )}

      {/* Add News Tab */}
      {activeTab === 'add' && (
        <div className={`rounded-xl ${isDark ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
          <div className="max-w-none">
            {/* Form Content */}
            <div className="p-6 space-y-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Title */}
                <div>
                  <label className={`block text-sm font-medium mb-2 ${
                    isDark ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Başlık *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      isDark
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    }`}
                    placeholder="Haber başlığını girin"
                    required
                  />
                </div>

                {/* Content */}
                <div>
                  <label className={`block text-sm font-medium mb-2 ${
                    isDark ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    İçerik *
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={12}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      isDark
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    }`}
                    placeholder="Haber içeriğinizi buraya yazın..."
                    required
                  />
                </div>

                {/* Categories and Location */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${
                      isDark ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      Kategori *
                    </label>
                    <select
                      value={formData.kategori}
                      onChange={(e) => handleInputChange('kategori', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        isDark
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      required
                    >
                      <option value="">Kategori Seçiniz</option>
                      {categories.map((category) => (
                        <option key={category} value={category}>
                          {category}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${
                      isDark ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      Şehir
                    </label>
                    <select
                      value={formData.sehir}
                      onChange={(e) => handleInputChange('sehir', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        isDark
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    >
                      <option value="">Şehir Seçiniz (İsteğe Bağlı)</option>
                      {cities.map((city) => (
                        <option key={city} value={city}>
                          {city}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Status and Breaking News */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${
                      isDark ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      Son Dakika
                    </label>
                    <select
                      value={formData.son_dakika}
                      onChange={(e) => handleInputChange('son_dakika', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        isDark
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    >
                      <option value="Hayır">Hayır</option>
                      <option value="Evet">Evet</option>
                    </select>
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${
                      isDark ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      Durum
                    </label>
                    <select
                      value={formData.status}
                      onChange={(e) => handleInputChange('status', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        isDark
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    >
                      <option value="active">Aktif</option>
                      <option value="inactive">Pasif</option>
                    </select>
                  </div>
                </div>

                {/* Submit Button */}
                <div className="flex items-center justify-between pt-4">
                  <div>
                    {editingNews && (
                      <button
                        type="button"
                        onClick={resetForm}
                        className={`text-sm ${
                          isDark ? 'text-gray-400 hover:text-gray-300' : 'text-gray-600 hover:text-gray-700'
                        }`}
                      >
                        Düzenlemeyi İptal Et
                      </button>
                    )}
                  </div>
                  <button
                    type="submit"
                    disabled={isSaving}
                    className={`flex items-center px-6 py-2 rounded-lg text-white font-medium transition-colors duration-200 ${
                      isSaving
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700'
                    }`}
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {isSaving ? 'Kaydediliyor...' : editingNews ? 'Güncelle' : 'Kaydet'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
