<?php
require_once '../config.php';

// Check admin authentication
session_start();
if (!isset($_SESSION['admin_user'])) {
    http_response_code(401);
    sendResponse(false, 'Yet<PERSON>iz erişim');
}

$pdo = getDBConnection();

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Get contacts with pagination
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = max(1, min(100, intval($_GET['limit'] ?? 10)));
    $status = $_GET['status'] ?? null;
    $offset = ($page - 1) * $limit;

    $whereClause = 'WHERE deleted = FALSE';
    $params = [];

    if ($status) {
        $whereClause .= ' AND status = ?';
        $params[] = $status;
    }

    // Get total count
    $countStmt = $pdo->prepare("SELECT COUNT(*) FROM contact_messages $whereClause");
    $countStmt->execute($params);
    $totalCount = $countStmt->fetchColumn();

    // Get contacts with site information
    $limit = (int)$limit;
    $offset = (int)$offset;
    // LIMIT ve OFFSET parametrelerini SQL'e göm
    $sql = "
        SELECT cm.id, cm.site_code, cm.full_name as name, cm.email, cm.phone,
               COALESCE(cm.subject, 'Genel İletişim') as subject, cm.message,
               cm.status, cm.is_read, cm.created_at, cm.message_type, cm.priority
        FROM contact_messages cm
        $whereClause
        ORDER BY cm.created_at DESC
        LIMIT $limit OFFSET $offset
    ";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $contacts = $stmt->fetchAll();

    // Add site names based on site_id
    foreach ($contacts as &$contact) {
        if ($contact['site_code'] === 'metaanaliz-group') {
            $contact['site_name'] = 'Meta Analiz Group';
        } elseif ($contact['site_code'] === 'metaanaliz-musavirlik') {
            $contact['site_name'] = 'Meta Analiz Müşavirlik';
        } else {
            $contact['site_name'] = 'Bilinmeyen Site';
        }
        $contact['source_table'] = 'contact_messages';
    }
    
    sendResponse(true, 'Contacts retrieved successfully', [
        'contacts' => $contacts,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => $totalCount,
            'totalPages' => ceil($totalCount / $limit)
        ]
    ]);
    
} elseif ($_SERVER['REQUEST_METHOD'] === 'PATCH') {
    // Update contact status or mark as read
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        sendResponse(false, 'Invalid JSON input');
    }

    $id = intval($input['id'] ?? 0);
    $action = sanitizeInput($input['action'] ?? '');

    if (!$id) {
        sendResponse(false, 'ID gerekli');
    }

    if ($action === 'mark_read') {
        // Mark as read
        $stmt = $pdo->prepare("UPDATE contact_messages SET is_read = TRUE, status = 'read' WHERE id = ?");
        $result = $stmt->execute([$id]);

        if ($result && $stmt->rowCount() > 0) {
            sendResponse(true, 'Mesaj okundu olarak işaretlendi');
        } else {
            sendResponse(false, 'Mesaj bulunamadı veya güncellenemedi');
        }
    } else {
        // Update status
        $status = sanitizeInput($input['status'] ?? '');

        if (!$status) {
            sendResponse(false, 'Durum gerekli');
        }

        $validStatuses = ['new', 'read', 'replied', 'closed'];
        if (!in_array($status, $validStatuses)) {
            sendResponse(false, 'Geçersiz durum');
        }

        $stmt = $pdo->prepare("UPDATE contact_messages SET status = ? WHERE id = ?");
        $result = $stmt->execute([$status, $id]);

        if ($result && $stmt->rowCount() > 0) {
            sendResponse(true, 'Durum güncellendi');
        } else {
            sendResponse(false, 'Mesaj bulunamadı veya güncellenemedi');
        }
    }

} elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    // Soft delete contact
    $id = intval($_GET['id'] ?? 0);

    if (!$id) {
        sendResponse(false, 'ID gerekli');
    }

    $stmt = $pdo->prepare("UPDATE contact_messages SET deleted = TRUE WHERE id = ?");
    $result = $stmt->execute([$id]);

    if ($result && $stmt->rowCount() > 0) {
        sendResponse(true, 'Mesaj silindi');
    } else {
        sendResponse(false, 'Mesaj bulunamadı veya silinemedi');
    }

} else {
    http_response_code(405);
    sendResponse(false, 'Method not allowed');
}
