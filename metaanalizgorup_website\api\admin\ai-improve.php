<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (empty($input['prompt']) || empty($input['api_key'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Prompt ve API anahtarı gerekli']);
    exit;
}

$prompt = $input['prompt'];
$model = $input['model'] ?? 'gpt-3.5-turbo';
$apiKey = $input['api_key'];

// OpenAI API'sine istek gönder
$ch = curl_init();

curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/chat/completions');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: Bearer ' . $apiKey
]);

$data = [
    'model' => $model,
    'messages' => [
        [
            'role' => 'user',
            'content' => $prompt
        ]
    ],
    'max_tokens' => 2000,
    'temperature' => 0.7
];

curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode !== 200) {
    $errorResponse = json_decode($response, true);
    $errorMessage = $errorResponse['error']['message'] ?? 'OpenAI API hatası';
    
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'AI servisi hatası: ' . $errorMessage
    ]);
    exit;
}

$responseData = json_decode($response, true);

if (!isset($responseData['choices'][0]['message']['content'])) {
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'AI yanıtı alınamadı'
    ]);
    exit;
}

$improvedText = trim($responseData['choices'][0]['message']['content']);

// Tırnak işaretlerini temizle
$improvedText = trim($improvedText, '"\'');

echo json_encode([
    'success' => true,
    'improved_text' => $improvedText,
    'usage' => $responseData['usage'] ?? null
]);
?>
