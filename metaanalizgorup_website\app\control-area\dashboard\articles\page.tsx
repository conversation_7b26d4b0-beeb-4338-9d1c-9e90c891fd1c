'use client';

import { useState, useEffect } from 'react';
import {
  PenTool,
  Plus,
  Edit,
  Trash2,
  Eye,
  Calendar,
  User,
  Search,
  Filter,
  CheckCircle,
  Clock,
  Archive,
  Save,
  Sparkles,
  Globe
} from 'lucide-react';

interface Article {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  author: string;
  category: string;
  status: 'draft' | 'published' | 'archived';
  is_featured: boolean;
  published_at: string | null;
  created_at: string;
  updated_at: string;
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string;
  site_code?: string; // 'mgsam' or 'metaanaliz'
}

export default function ArticlesPage() {
  const [articles, setArticles] = useState<Article[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDark, setIsDark] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'draft' | 'published' | 'archived'>('all');
  const [activeTab, setActiveTab] = useState<'list' | 'add'>('add');
  const [editingArticle, setEditingArticle] = useState<Article | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    author: 'Meta Analiz Müşavirlik',
    category: 'Makalelerimiz',
    status: 'draft' as 'draft' | 'published' | 'archived',
    is_featured: false,
    meta_title: '',
    meta_description: '',
    meta_keywords: '',
    site_code: 'metaanaliz' as 'mgsam' | 'metaanaliz'
  });
  const [isSaving, setIsSaving] = useState(false);
  const [isImproving, setIsImproving] = useState<'title' | 'excerpt' | 'content' | null>(null);

  // Theme detection
  useEffect(() => {
    const checkTheme = () => {
      const isDarkMode = document.documentElement.classList.contains('dark') || 
                        document.body.classList.contains('dark');
      setIsDark(isDarkMode);
    };

    checkTheme();
    
    const observer = new MutationObserver(checkTheme);
    observer.observe(document.documentElement, { 
      attributes: true, 
      attributeFilter: ['class'] 
    });
    observer.observe(document.body, { 
      attributes: true, 
      attributeFilter: ['class'] 
    });

    window.addEventListener('themeChange', checkTheme);

    return () => {
      observer.disconnect();
      window.removeEventListener('themeChange', checkTheme);
    };
  }, []);

  // Load articles
  useEffect(() => {
    loadArticles();
  }, []);

  const loadArticles = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/admin/articles');
      if (response.ok) {
        const data = await response.json();
        setArticles(data.articles || []);
      }
    } catch (error) {
      console.error('Articles loading error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Generate slug from title
  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/ğ/g, 'g')
      .replace(/ü/g, 'u')
      .replace(/ş/g, 's')
      .replace(/ı/g, 'i')
      .replace(/ö/g, 'o')
      .replace(/ç/g, 'c')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  // Handle form input changes
  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value };

      // Auto-generate slug when title changes
      if (field === 'title' && typeof value === 'string') {
        updated.slug = generateSlug(value);
        // Auto-generate meta_title if empty
        if (!prev.meta_title) {
          updated.meta_title = `${value} | Meta Analiz Müşavirlik`;
        }
      }

      return updated;
    });
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      title: '',
      slug: '',
      excerpt: '',
      content: '',
      author: 'Meta Analiz Müşavirlik',
      category: 'Makalelerimiz',
      status: 'draft',
      is_featured: false,
      meta_title: '',
      meta_description: '',
      meta_keywords: '',
      site_code: 'metaanaliz'
    });
    setEditingArticle(null);
  };

  // AI improvement function
  const improveWithAI = async (field: 'title' | 'excerpt' | 'content') => {
    const currentValue = formData[field];
    if (!currentValue.trim()) {
      alert('Lütfen önce metin girin.');
      return;
    }

    setIsImproving(field);

    try {
      // Get AI settings from localStorage or API
      const aiSettings = JSON.parse(localStorage.getItem('ai-settings') || '{"selectedModel": "gpt-4o", "systemPrompt": "Sen yardımcı bir AI asistanısın. Türkçe olarak profesyonel ve yararlı yanıtlar veriyorsun."}');

      // Simulate AI improvement - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      let improvedText = '';
      if (field === 'title') {
        improvedText = `${currentValue} - AI ile İyileştirildi`;
      } else if (field === 'excerpt') {
        improvedText = `${currentValue}\n\n[Bu kısa açıklama AI tarafından iyileştirilmiştir.]`;
      } else {
        improvedText = `${currentValue}\n\n[Bu içerik AI tarafından iyileştirilmiş ve daha profesyonel bir dil kullanılmıştır.]`;
      }

      setFormData(prev => ({ ...prev, [field]: improvedText }));
    } catch (error) {
      console.error('AI improvement error:', error);
      alert('AI iyileştirme sırasında hata oluştu.');
    } finally {
      setIsImproving(null);
    }
  };

  // Open edit form
  const openEditForm = (article: Article) => {
    setFormData({
      title: article.title,
      slug: article.slug,
      excerpt: article.excerpt,
      content: article.content,
      author: article.author,
      category: article.category,
      status: article.status,
      is_featured: article.is_featured,
      meta_title: article.meta_title || '',
      meta_description: article.meta_description || '',
      meta_keywords: article.meta_keywords || '',
      site_code: (article.site_code as 'mgsam' | 'metaanaliz') || 'metaanaliz'
    });
    setEditingArticle(article);
    setActiveTab('add');
  };

  // Save article
  const saveArticle = async () => {
    if (!formData.title.trim() || !formData.content.trim()) {
      alert('Başlık ve içerik alanları zorunludur.');
      return;
    }

    try {
      setIsSaving(true);

      const url = editingArticle
        ? `/api/admin/articles/${editingArticle.id}`
        : '/api/admin/articles';

      const method = editingArticle ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        await loadArticles();
        resetForm();
        setActiveTab('list');
        alert(editingArticle ? 'Makale güncellendi!' : 'Makale eklendi!');
      } else {
        const error = await response.json();
        alert('Hata: ' + (error.message || 'Bilinmeyen hata'));
      }
    } catch (error) {
      console.error('Save error:', error);
      alert('Kaydetme sırasında hata oluştu.');
    } finally {
      setIsSaving(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'published':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'draft':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'archived':
        return <Archive className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";
    switch (status) {
      case 'published':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'draft':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case 'archived':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const filteredArticles = articles.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         article.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || article.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const stats = {
    total: articles.length,
    published: articles.filter(a => a.status === 'published').length,
    draft: articles.filter(a => a.status === 'draft').length,
    archived: articles.filter(a => a.status === 'archived').length
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDark ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'
    }`}>
      <div className="p-6">
      {/* Tabs */}
      <div className="mb-8">
        <div className={`border-b ${
          isDark ? 'border-gray-700' : 'border-gray-200'
        }`}>
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('add')}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                  activeTab === 'add'
                    ? isDark
                      ? 'border-blue-400 text-blue-400'
                      : 'border-blue-500 text-blue-600'
                    : isDark
                      ? 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Plus className="h-4 w-4 mr-1 inline" />
                Yeni Makale
              </button>
              <button
                onClick={() => setActiveTab('list')}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                  activeTab === 'list'
                    ? isDark
                      ? 'border-blue-400 text-blue-400'
                      : 'border-blue-500 text-blue-600'
                    : isDark
                      ? 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Tüm Makaleler
              </button>
            </nav>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'list' && (
        <>
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className={`p-6 rounded-xl ${
              isDark ? 'bg-gray-800' : 'bg-white'
            } shadow-sm`}>
          <div className="flex items-center">
            <PenTool className={`h-8 w-8 ${
              isDark ? 'text-blue-400' : 'text-blue-600'
            }`} />
            <div className="ml-4">
              <p className={`text-sm font-medium ${
                isDark ? 'text-gray-400' : 'text-gray-600'
              }`}>
                Toplam Makale
              </p>
              <p className={`text-2xl font-bold ${
                isDark ? 'text-white' : 'text-gray-900'
              }`}>
                {stats.total}
              </p>
            </div>
          </div>
            </div>

            <div className={`p-6 rounded-xl ${
              isDark ? 'bg-gray-800' : 'bg-white'
            } shadow-sm`}>
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-500" />
                <div className="ml-4">
                  <p className={`text-sm font-medium ${
                    isDark ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Yayınlanan
                  </p>
                  <p className={`text-2xl font-bold ${
                    isDark ? 'text-white' : 'text-gray-900'
                  }`}>
                    {stats.published}
                  </p>
                </div>
              </div>
            </div>

            <div className={`p-6 rounded-xl ${
              isDark ? 'bg-gray-800' : 'bg-white'
            } shadow-sm`}>
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-500" />
                <div className="ml-4">
                  <p className={`text-sm font-medium ${
                    isDark ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Taslak
                  </p>
                  <p className={`text-2xl font-bold ${
                    isDark ? 'text-white' : 'text-gray-900'
                  }`}>
                    {stats.draft}
                  </p>
                </div>
              </div>
            </div>

            <div className={`p-6 rounded-xl ${
              isDark ? 'bg-gray-800' : 'bg-white'
            } shadow-sm`}>
              <div className="flex items-center">
                <Archive className="h-8 w-8 text-gray-500" />
                <div className="ml-4">
                  <p className={`text-sm font-medium ${
                    isDark ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Arşivlenen
                  </p>
                  <p className={`text-2xl font-bold ${
                    isDark ? 'text-white' : 'text-gray-900'
                  }`}>
                    {stats.archived}
                  </p>
                </div>
              </div>
            </div>
          </div>

      {/* Filters */}
      <div className={`p-6 rounded-xl mb-6 ${
        isDark ? 'bg-gray-800' : 'bg-white'
      } shadow-sm`}>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 ${
                isDark ? 'text-gray-400' : 'text-gray-500'
              }`} />
              <input
                type="text"
                placeholder="Makale ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  isDark 
                    ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' 
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                }`}
              />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Filter className={`h-5 w-5 ${
              isDark ? 'text-gray-400' : 'text-gray-500'
            }`} />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className={`px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                isDark 
                  ? 'bg-gray-700 border-gray-600 text-white' 
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              <option value="all">Tüm Durumlar</option>
              <option value="published">Yayınlanan</option>
              <option value="draft">Taslak</option>
              <option value="archived">Arşivlenen</option>
            </select>
          </div>
        </div>
      </div>

      {/* Articles List */}
      <div className={`rounded-xl ${
        isDark ? 'bg-gray-800' : 'bg-white'
      } shadow-sm overflow-hidden`}>
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className={`mt-4 ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
              Makaleler yükleniyor...
            </p>
          </div>
        ) : filteredArticles.length === 0 ? (
          <div className="p-8 text-center">
            <PenTool className={`h-12 w-12 mx-auto mb-4 ${
              isDark ? 'text-gray-600' : 'text-gray-400'
            }`} />
            <p className={`text-lg font-medium ${
              isDark ? 'text-gray-400' : 'text-gray-600'
            }`}>
              {searchTerm || statusFilter !== 'all' ? 'Arama kriterlerine uygun makale bulunamadı' : 'Henüz makale eklenmemiş'}
            </p>
            {!searchTerm && statusFilter === 'all' && (
              <button
                onClick={() => setActiveTab('add')}
                className="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
              >
                <Plus className="h-5 w-5 mr-2" />
                İlk Makaleyi Ekle
              </button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className={`${
                isDark ? 'bg-gray-700' : 'bg-gray-50'
              }`}>
                <tr>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    isDark ? 'text-gray-300' : 'text-gray-500'
                  }`}>
                    Makale
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    isDark ? 'text-gray-300' : 'text-gray-500'
                  }`}>
                    Site
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    isDark ? 'text-gray-300' : 'text-gray-500'
                  }`}>
                    Durum
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    isDark ? 'text-gray-300' : 'text-gray-500'
                  }`}>
                    Tarih
                  </th>
                  <th className={`px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                    isDark ? 'text-gray-300' : 'text-gray-500'
                  }`}>
                    İşlemler
                  </th>
                </tr>
              </thead>
              <tbody className={`divide-y ${
                isDark ? 'divide-gray-700' : 'divide-gray-200'
              }`}>
                {filteredArticles.map((article) => (
                  <tr key={article.id} className={`hover:${
                    isDark ? 'bg-gray-700' : 'bg-gray-50'
                  } transition-colors duration-200`}>
                    <td className="px-6 py-4">
                      <div className="flex items-start space-x-3">
                        <div className="flex-1 min-w-0">
                          <p className={`text-sm font-medium ${
                            isDark ? 'text-white' : 'text-gray-900'
                          } truncate`}>
                            {article.title}
                          </p>
                          <p className={`text-sm ${
                            isDark ? 'text-gray-400' : 'text-gray-500'
                          } line-clamp-2`}>
                            {article.excerpt}
                          </p>
                          <div className="flex items-center mt-1 space-x-2">
                            <User className={`h-3 w-3 ${
                              isDark ? 'text-gray-500' : 'text-gray-400'
                            }`} />
                            <span className={`text-xs ${
                              isDark ? 'text-gray-500' : 'text-gray-400'
                            }`}>
                              {article.author}
                            </span>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        <Globe className={`h-4 w-4 ${
                          isDark ? 'text-gray-400' : 'text-gray-500'
                        }`} />
                        <span className={`text-sm px-2 py-1 rounded-full ${
                          article.site_code === 'mgsam'
                            ? isDark
                              ? 'bg-green-900/20 text-green-400 border border-green-800'
                              : 'bg-green-100 text-green-800 border border-green-200'
                            : isDark
                              ? 'bg-blue-900/20 text-blue-400 border border-blue-800'
                              : 'bg-blue-100 text-blue-800 border border-blue-200'
                        }`}>
                          {article.site_code === 'mgsam' ? 'MGSAM' : 'Meta Analiz'}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(article.status)}
                        <span className={getStatusBadge(article.status)}>
                          {article.status === 'published' ? 'Yayınlandı' :
                           article.status === 'draft' ? 'Taslak' : 'Arşivlendi'}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-1">
                        <Calendar className={`h-4 w-4 ${
                          isDark ? 'text-gray-500' : 'text-gray-400'
                        }`} />
                        <span className={`text-sm ${
                          isDark ? 'text-gray-400' : 'text-gray-500'
                        }`}>
                          {article.published_at ? 
                            new Date(article.published_at).toLocaleDateString('tr-TR') :
                            new Date(article.created_at).toLocaleDateString('tr-TR')
                          }
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => window.open(`https://metaanalizmusavirlik.com/makalelerimiz/${article.slug}`, '_blank')}
                          className={`p-2 rounded-lg transition-colors duration-200 ${
                            isDark 
                              ? 'text-gray-400 hover:text-blue-400 hover:bg-gray-700' 
                              : 'text-gray-500 hover:text-blue-600 hover:bg-gray-100'
                          }`}
                          title="Görüntüle"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => openEditForm(article)}
                          className={`p-2 rounded-lg transition-colors duration-200 ${
                            isDark
                              ? 'text-gray-400 hover:text-green-400 hover:bg-gray-700'
                              : 'text-gray-500 hover:text-green-600 hover:bg-gray-100'
                          }`}
                          title="Düzenle"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => {/* Delete handler */}}
                          className={`p-2 rounded-lg transition-colors duration-200 ${
                            isDark 
                              ? 'text-gray-400 hover:text-red-400 hover:bg-gray-700' 
                              : 'text-gray-500 hover:text-red-600 hover:bg-gray-100'
                          }`}
                          title="Sil"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
        </>
      )}

      {/* Add Article Tab */}
      {activeTab === 'add' && (
        <div className={`rounded-xl ${
          isDark ? 'bg-gray-800' : 'bg-white'
        } shadow-sm`}>
          <div className="max-w-none">
            {/* Form Content */}
            <div className="p-6 space-y-6">
              {/* Site Selection */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${
                  isDark ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  Yayınlanacak Site *
                </label>
                <div className="grid grid-cols-2 gap-4">
                  <label className={`flex items-center p-4 rounded-lg border cursor-pointer transition-colors ${
                    formData.site_code === 'metaanaliz'
                      ? isDark
                        ? 'border-blue-500 bg-blue-500/10'
                        : 'border-blue-500 bg-blue-50'
                      : isDark
                        ? 'border-gray-600 hover:border-gray-500'
                        : 'border-gray-200 hover:border-gray-300'
                  }`}>
                    <input
                      type="radio"
                      name="site_code"
                      value="metaanaliz"
                      checked={formData.site_code === 'metaanaliz'}
                      onChange={(e) => setFormData(prev => ({ ...prev, site_code: e.target.value as 'mgsam' | 'metaanaliz' }))}
                      className="sr-only"
                    />
                    <div className="flex-1">
                      <div className={`text-sm font-medium ${
                        isDark ? 'text-white' : 'text-gray-900'
                      }`}>
                        Meta Analiz Müşavirlik
                      </div>
                      <div className={`text-xs ${
                        isDark ? 'text-gray-400' : 'text-gray-500'
                      }`}>
                        metaanalizmusavirlik.com
                      </div>
                    </div>
                    {formData.site_code === 'metaanaliz' && (
                      <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      </div>
                    )}
                  </label>

                  <label className={`flex items-center p-4 rounded-lg border cursor-pointer transition-colors ${
                    formData.site_code === 'mgsam'
                      ? isDark
                        ? 'border-blue-500 bg-blue-500/10'
                        : 'border-blue-500 bg-blue-50'
                      : isDark
                        ? 'border-gray-600 hover:border-gray-500'
                        : 'border-gray-200 hover:border-gray-300'
                  }`}>
                    <input
                      type="radio"
                      name="site_code"
                      value="mgsam"
                      checked={formData.site_code === 'mgsam'}
                      onChange={(e) => setFormData(prev => ({ ...prev, site_code: e.target.value as 'mgsam' | 'metaanaliz' }))}
                      className="sr-only"
                    />
                    <div className="flex-1">
                      <div className={`text-sm font-medium ${
                        isDark ? 'text-white' : 'text-gray-900'
                      }`}>
                        MGSAM
                      </div>
                      <div className={`text-xs ${
                        isDark ? 'text-gray-400' : 'text-gray-500'
                      }`}>
                        mgsam.com
                      </div>
                    </div>
                    {formData.site_code === 'mgsam' && (
                      <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      </div>
                    )}
                  </label>
                </div>
              </div>

              {/* Title */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${
                  isDark ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  Başlık *
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className={`w-full px-3 py-2 pr-12 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      isDark
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    }`}
                    placeholder="Makale başlığını girin"
                  />
                  <button
                    type="button"
                    onClick={() => improveWithAI('title')}
                    disabled={!formData.title.trim() || isImproving === 'title'}
                    className={`absolute right-2 top-1/2 transform -translate-y-1/2 p-1.5 rounded transition-colors ${
                      !formData.title.trim() || isImproving === 'title'
                        ? isDark
                          ? 'text-gray-600 cursor-not-allowed'
                          : 'text-gray-400 cursor-not-allowed'
                        : isDark
                          ? 'text-purple-400 hover:text-purple-300 hover:bg-gray-600'
                          : 'text-purple-600 hover:text-purple-700 hover:bg-purple-50'
                    }`}
                    title="AI ile İyileştir"
                  >
                    <Sparkles className={`w-4 h-4 ${
                      isImproving === 'title' ? 'animate-spin' : ''
                    }`} />
                  </button>
                </div>
              </div>

              {/* Content */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${
                  isDark ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  İçerik *
                </label>
                <div className="relative">
                  <textarea
                    value={formData.content}
                    onChange={(e) => handleInputChange('content', e.target.value)}
                    rows={12}
                    className={`w-full px-3 py-2 pr-12 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm resize-none ${
                      isDark
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    }`}
                    placeholder="Makale içeriğinizi buraya yazın..."
                  />
                  <button
                    type="button"
                    onClick={() => improveWithAI('content')}
                    disabled={!formData.content.trim() || isImproving === 'content'}
                    className={`absolute right-2 top-2 p-1.5 rounded transition-colors ${
                      !formData.content.trim() || isImproving === 'content'
                        ? isDark
                          ? 'text-gray-600 cursor-not-allowed'
                          : 'text-gray-400 cursor-not-allowed'
                        : isDark
                          ? 'text-purple-400 hover:text-purple-300 hover:bg-gray-600'
                          : 'text-purple-600 hover:text-purple-700 hover:bg-purple-50'
                    }`}
                    title="AI ile İyileştir"
                  >
                    <Sparkles className={`w-4 h-4 ${
                      isImproving === 'content' ? 'animate-spin' : ''
                    }`} />
                  </button>
                </div>
                <p className={`text-xs mt-1 ${
                  isDark ? 'text-gray-400' : 'text-gray-500'
                }`}>
                  Makale içeriğinizi normal metin olarak yazabilirsiniz. Paragraflar otomatik olarak düzenlenecektir.
                </p>
              </div>

              {/* Author */}
              <div>
                <div>
                  <label className={`block text-sm font-medium mb-2 ${
                    isDark ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Yazar Adı *
                  </label>
                  <input
                    type="text"
                    value={formData.author}
                    onChange={(e) => handleInputChange('author', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      isDark
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    }`}
                    placeholder="Makale yazarının adını girin"
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${
                    isDark ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Kategori *
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      isDark
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  >
                    <option value="">Kategori seçin</option>
                    <option value="Yönetim ve Yatırım Müşavirliği">Yönetim ve Yatırım Müşavirliği</option>
                    <option value="Finansal ve Mali Danışmanlık/Müşavirlik">Finansal ve Mali Danışmanlık/Müşavirlik</option>
                    <option value="İş ve Sosyal Güvenlik Müşavirliği">İş ve Sosyal Güvenlik Müşavirliği</option>
                    <option value="İnsan Kaynakları">İnsan Kaynakları</option>
                    <option value="Kurumsal Danışmanlık">Kurumsal Danışmanlık</option>
                    <option value="Genel">Genel</option>
                  </select>
                </div>
              </div>

              {/* Featured */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_featured"
                  checked={formData.is_featured}
                  onChange={(e) => handleInputChange('is_featured', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="is_featured" className={`ml-2 text-sm ${
                  isDark ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  Öne çıkarılmış makale
                </label>
              </div>


            </div>

            {/* Footer */}
            <div className={`flex items-center justify-end space-x-3 p-6 border-t ${
              isDark ? 'border-gray-700' : 'border-gray-200'
            }`}>
              <button
                onClick={resetForm}
                className={`px-4 py-2 border rounded-lg transition-colors duration-200 ${
                  isDark
                    ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                Temizle
              </button>
              <button
                onClick={saveArticle}
                disabled={isSaving}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Kaydediliyor...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    {editingArticle ? 'Güncelle' : 'Kaydet'}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
      </div>
    </div>
  );
}
