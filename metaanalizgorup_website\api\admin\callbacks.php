<?php
require_once '../config.php';

// Check admin authentication
session_start();
if (!isset($_SESSION['admin_user'])) {
    http_response_code(401);
    sendResponse(false, 'Yet<PERSON><PERSON> erişim');
}

$pdo = getDBConnection();

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Get callbacks with pagination
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = max(1, min(100, intval($_GET['limit'] ?? 10)));
    $status = $_GET['status'] ?? null;
    $offset = ($page - 1) * $limit;
    
    $whereClause = 'WHERE deleted = FALSE';
    $params = [];

    if ($status) {
        $whereClause .= ' AND status = ?';
        $params[] = $status;
    }

    // Get total count
    $countStmt = $pdo->prepare("SELECT COUNT(*) FROM callback_requests $whereClause");
    $countStmt->execute($params);
    $totalCount = $countStmt->fetchColumn();

    // Get callbacks with site information
    $limit = (int)$limit;
    $offset = (int)$offset;
    $sql = "
        SELECT cr.id, cr.site_code, cr.name, cr.phone, cr.preferred_time, cr.message,
               cr.status, cr.is_read, cr.created_at
        FROM callback_requests cr
        $whereClause
        ORDER BY cr.created_at DESC
        LIMIT $limit OFFSET $offset
    ";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $callbacks = $stmt->fetchAll();
    // Add site names if missing
    foreach ($callbacks as &$callback) {
        if ($callback['site_code'] === 'metaanaliz-group') {
            $callback['site_name'] = 'Meta Analiz Group';
        } elseif ($callback['site_code'] === 'metaanaliz-musavirlik') {
            $callback['site_name'] = 'Meta Analiz Müşavirlik';
        } else {
            $callback['site_name'] = 'Bilinmeyen Site';
        }
    }

    sendResponse(true, 'Geri arama talepleri başarıyla alındı', [
        'callbacks' => $callbacks,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => ceil($totalCount / $limit),
            'total_count' => $totalCount,
            'per_page' => $limit
        ]
    ]);

} elseif ($_SERVER['REQUEST_METHOD'] === 'PATCH') {
    // Handle mark as read
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['id']) || !isset($input['action'])) {
        sendResponse(false, 'Geçersiz istek');
    }
    
    $callbackId = intval($input['id']);
    $action = $input['action'];
    
    if ($action === 'mark_read') {
        try {
            $stmt = $pdo->prepare("UPDATE callback_requests SET is_read = TRUE WHERE id = ?");
            $stmt->execute([$callbackId]);

            if ($stmt->rowCount() > 0) {
                sendResponse(true, 'Geri arama talebi okundu olarak işaretlendi');
            } else {
                sendResponse(false, 'Geri arama talebi bulunamadı');
            }
        } catch (PDOException $e) {
            error_log('Callback update error: ' . $e->getMessage());
            sendResponse(false, 'Güncelleme sırasında hata oluştu');
        }
    } else {
        sendResponse(false, 'Geçersiz işlem');
    }

} else {
    http_response_code(405);
    sendResponse(false, 'Method not allowed');
}
