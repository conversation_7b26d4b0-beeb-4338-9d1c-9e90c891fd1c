<?php
session_start();
require_once 'config.php';

// CORS ayarları
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// OPTIONS request için erken yanıt
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Sadece POST istekleri kabul edilir.');
}

// Get client IP
$clientIP = getClientIP();

// Check rate limit - daha esnek limit
if (!rateLimitCheck($clientIP, 10, 600)) { // 10 requests per 10 minutes
    sendResponse(false, 'Çok fazla istek gönderdiniz. Lütfen daha sonra tekrar deneyin.');
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    sendResponse(false, 'Geçersiz JSON verisi.');
}

// Validate required fields - sadece temel alanlar zorunlu
$requiredFields = ['name', 'email', 'service', 'meeting_date', 'meeting_time'];
foreach ($requiredFields as $field) {
    if (empty($input[$field])) {
        sendResponse(false, 'Ad Soyad, E-posta, Hizmet, Toplantı Tarihi ve Saati alanları zorunludur.');
    }
}

// Sanitize inputs
$name = sanitizeInput($input['name']);
$email = sanitizeInput($input['email']);
$phone = isset($input['phone']) ? sanitizeInput($input['phone']) : '';
$company = isset($input['company']) ? sanitizeInput($input['company']) : '';
$service = sanitizeInput($input['service']);
$meetingDate = sanitizeInput($input['meeting_date']);
$meetingTime = sanitizeInput($input['meeting_time']);
$message = isset($input['message']) ? sanitizeInput($input['message']) : '';
$notes = isset($input['notes']) ? sanitizeInput($input['notes']) : '';

// Validate email
if (!validateEmail($email)) {
    sendResponse(false, 'Geçerli bir e-posta adresi giriniz.');
}

// Validate phone (only if provided)
if (!empty($phone) && !validatePhone($phone)) {
    sendResponse(false, 'Geçerli bir telefon numarası giriniz.');
}

// Validate date
$dateObj = DateTime::createFromFormat('Y-m-d', $meetingDate);
if (!$dateObj || $dateObj->format('Y-m-d') !== $meetingDate) {
    sendResponse(false, 'Geçerli bir tarih giriniz.');
}

// Check if date is in the future
$today = new DateTime();
if ($dateObj < $today) {
    sendResponse(false, 'Toplantı tarihi bugünden sonra olmalıdır.');
}

// Validate time
if (!preg_match('/^([01]?\d|2[0-3]):[0-5]\d$/', $meetingTime)) {
    sendResponse(false, 'Geçerli bir saat giriniz (HH:MM formatında).');
}

// Validate service
$validServices = [
    'Stratejik Danışmanlık',
    'İş Geliştirme',
    'Dijital Dönüşüm',
    'Proje Yönetimi',
    'Eğitim ve Gelişim',
    'Diğer'
];

if (!in_array($service, $validServices)) {
    sendResponse(false, 'Geçerli bir hizmet seçiniz.');
}

// Validate lengths
if (strlen($name) < 2 || strlen($name) > 100) {
    sendResponse(false, 'İsim 2-100 karakter arasında olmalıdır.');
}

if (!empty($company) && (strlen($company) < 2 || strlen($company) > 100)) {
    sendResponse(false, 'Şirket adı 2-100 karakter arasında olmalıdır.');
}

if (!empty($message) && (strlen($message) < 10 || strlen($message) > 1000)) {
    sendResponse(false, 'Mesaj 10-1000 karakter arasında olmalıdır.');
}

try {
    $pdo = getDBConnection();
    
    // Check for existing meeting at the same time
    $stmt = $pdo->prepare("
        SELECT COUNT(*) FROM meeting_requests
        WHERE meeting_date = ? AND meeting_time = ? AND status IN ('pending', 'confirmed')
    ");
    $stmt->execute([$meetingDate, $meetingTime]);
    
    if ($stmt->fetchColumn() > 0) {
        sendResponse(false, 'Bu tarih ve saatte zaten bir toplantı planlanmış. Lütfen farklı bir zaman seçiniz.');
    }
    
    // Site ID'sini al (Meta Analiz Group için)
    $siteId = 1; // Meta Analiz Group site ID'si

    // Insert meeting request
    $stmt = $pdo->prepare("
        INSERT INTO meeting_requests (
            site_id, name, email, phone, company, service, meeting_date, meeting_time,
            message, notes, created_at, status, ip_address, user_agent
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), 'pending', ?, ?)
    ");
    
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    $stmt->execute([
        $siteId,
        $name,
        $email,
        $phone,
        $company,
        $service,
        $meetingDate,
        $meetingTime,
        $message,
        $notes,
        $clientIP,
        $userAgent
    ]);
    
    $meetingId = $pdo->lastInsertId();
    
    // Send email notification
    $emailSent = sendMeetingEmailNotification([
        'id' => $meetingId,
        'name' => $name,
        'email' => $email,
        'phone' => $phone,
        'company' => $company,
        'service' => $service,
        'meeting_date' => $meetingDate,
        'meeting_time' => $meetingTime,
        'message' => $message,
        'notes' => $notes
    ]);
    
    sendResponse(true, 'Toplantı talebiniz başarıyla gönderildi. Size en kısa sürede dönüş yapacağız.', [
        'id' => $meetingId,
        'email_sent' => $emailSent
    ]);
    
} catch (PDOException $e) {
    error_log('Meeting request error: ' . $e->getMessage());
    sendResponse(false, 'Toplantı talebi gönderilirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.');
}

function sendMeetingEmailNotification($data) {
    $to = '<EMAIL>';
    $subject = 'Yeni Toplantı Talebi - ' . $data['company'];
    
    $message = "
    Yeni bir toplantı talebi alındı:
    
    ID: {$data['id']}
    İsim: {$data['name']}
    E-posta: {$data['email']}
    Telefon: {$data['phone']}
    Şirket: {$data['company']}
    Hizmet: {$data['service']}
    
    Toplantı Tarihi: {$data['meeting_date']}
    Toplantı Saati: {$data['meeting_time']}
    
    Mesaj:
    {$data['message']}
    
    Notlar:
    {$data['notes']}
    
    Talep Tarihi: " . date('d.m.Y H:i:s') . "
    ";
    
    $headers = [
        'From: <EMAIL>',
        'Reply-To: ' . $data['email'],
        'Content-Type: text/plain; charset=UTF-8'
    ];

    return mail($to, $subject, $message, implode("\r\n", $headers));
}
